<template>
  <div class="amazon-config-container">
    <div class="page-header">
      <h1>Amazon API 配置管理</h1>
      <p>配置和管理亚马逊SP-API密钥、区域设置和API权限</p>
    </div>

    <!-- API配置卡片 -->
    <div class="config-cards">
      <a-row :gutter="24">
        <a-col :span="8">
          <a-card title="SP-API 配置" class="config-card">
            <div class="config-item">
              <a-badge :status="spApiStatus" />
              <span class="config-label">SP-API状态</span>
            </div>
            <div class="config-actions">
              <a-button type="primary" @click="showSpApiModal = true">
                配置SP-API
              </a-button>
              <a-button @click="testSpApi">测试连接</a-button>
            </div>
          </a-card>
        </a-col>

        <a-col :span="8">
          <a-card title="广告API配置" class="config-card">
            <div class="config-item">
              <a-badge :status="adsApiStatus" />
              <span class="config-label">广告API状态</span>
            </div>
            <div class="config-actions">
              <a-button type="primary" @click="showAdsApiModal = true">
                配置广告API
              </a-button>
              <a-button @click="testAdsApi">测试连接</a-button>
            </div>
          </a-card>
        </a-col>

        <a-col :span="8">
          <a-card title="区域设置" class="config-card">
            <div class="config-item">
              <span class="config-label">当前区域: {{ currentRegion }}</span>
            </div>
            <div class="config-actions">
              <a-button type="primary" @click="showRegionModal = true">
                切换区域
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- API密钥列表 -->
    <a-card title="API密钥管理" class="api-keys-card">
      <template #extra>
        <a-button type="primary" @click="addApiKey">
          <template #icon><PlusOutlined /></template>
          添加密钥
        </a-button>
      </template>

      <a-table 
        :columns="apiKeyColumns" 
        :data-source="apiKeys" 
        :pagination="false"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-badge :status="record.status === 'active' ? 'success' : 'error'" />
            <span>{{ record.status === 'active' ? '正常' : '异常' }}</span>
          </template>
          
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button size="small" @click="editApiKey(record)">编辑</a-button>
              <a-button size="small" @click="testApiKey(record)">测试</a-button>
              <a-button size="small" danger @click="deleteApiKey(record)">删除</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- SP-API配置模态框 -->
    <a-modal
      v-model:open="showSpApiModal"
      title="SP-API 配置"
      width="600px"
      @ok="saveSpApiConfig"
    >
      <a-form :model="spApiForm" layout="vertical">
        <a-form-item label="Client ID" required>
          <a-input v-model:value="spApiForm.clientId" placeholder="输入Client ID" />
        </a-form-item>
        
        <a-form-item label="Client Secret" required>
          <a-input-password v-model:value="spApiForm.clientSecret" placeholder="输入Client Secret" />
        </a-form-item>
        
        <a-form-item label="Refresh Token" required>
          <a-input-password v-model:value="spApiForm.refreshToken" placeholder="输入Refresh Token" />
        </a-form-item>
        
        <a-form-item label="区域" required>
          <a-select v-model:value="spApiForm.region" placeholder="选择区域">
            <a-select-option value="us-east-1">北美 (US)</a-select-option>
            <a-select-option value="eu-west-1">欧洲 (EU)</a-select-option>
            <a-select-option value="us-west-2">远东 (FE)</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="环境">
          <a-radio-group v-model:value="spApiForm.environment">
            <a-radio value="sandbox">沙盒环境</a-radio>
            <a-radio value="production">生产环境</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- API测试结果 -->
    <a-card v-if="testResults.length > 0" title="API测试结果" class="test-results-card">
      <a-timeline>
        <a-timeline-item 
          v-for="result in testResults" 
          :key="result.id"
          :color="result.success ? 'green' : 'red'"
        >
          <div class="test-result-item">
            <div class="test-header">
              <span class="test-api">{{ result.api }}</span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="test-message">{{ result.message }}</div>
            <div v-if="result.data" class="test-data">
              <a-typography-paragraph copyable>
                <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
              </a-typography-paragraph>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'

// 响应式数据
const showSpApiModal = ref(false)
const showAdsApiModal = ref(false)
const showRegionModal = ref(false)

const spApiStatus = ref('success')
const adsApiStatus = ref('error')
const currentRegion = ref('北美 (US)')

const spApiForm = reactive({
  clientId: '',
  clientSecret: '',
  refreshToken: '',
  region: 'us-east-1',
  environment: 'sandbox'
})

const testResults = ref([])

// API密钥表格配置
const apiKeyColumns = [
  { title: 'API类型', dataIndex: 'type', key: 'type' },
  { title: '名称', dataIndex: 'name', key: 'name' },
  { title: '区域', dataIndex: 'region', key: 'region' },
  { title: '环境', dataIndex: 'environment', key: 'environment' },
  { title: '状态', key: 'status' },
  { title: '最后测试', dataIndex: 'lastTest', key: 'lastTest' },
  { title: '操作', key: 'actions' }
]

const apiKeys = ref([
  {
    id: 1,
    type: 'SP-API',
    name: '北美SP-API',
    region: 'us-east-1',
    environment: 'production',
    status: 'active',
    lastTest: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    type: '广告API',
    name: '北美广告API',
    region: 'us-east-1', 
    environment: 'production',
    status: 'error',
    lastTest: '2024-01-15 09:15:00'
  }
])

// 方法
const saveSpApiConfig = () => {
  // 保存SP-API配置逻辑
  message.success('SP-API配置保存成功')
  showSpApiModal.value = false
}

const testSpApi = async () => {
  message.loading('正在测试SP-API连接...', 0)
  
  // 模拟API测试
  setTimeout(() => {
    message.destroy()
    const result = {
      id: Date.now(),
      api: 'SP-API',
      success: true,
      message: 'SP-API连接测试成功',
      timestamp: new Date().toLocaleString(),
      data: {
        marketplaceId: 'ATVPDKIKX0DER',
        sellerId: 'A1234567890123',
        status: 'ACTIVE'
      }
    }
    testResults.value.unshift(result)
    message.success('SP-API测试成功')
  }, 2000)
}

const testAdsApi = async () => {
  message.loading('正在测试广告API连接...', 0)
  
  setTimeout(() => {
    message.destroy()
    const result = {
      id: Date.now(),
      api: '广告API',
      success: false,
      message: '广告API连接失败: 无效的访问令牌',
      timestamp: new Date().toLocaleString()
    }
    testResults.value.unshift(result)
    message.error('广告API测试失败')
  }, 1500)
}

const addApiKey = () => {
  showSpApiModal.value = true
}

const editApiKey = (record) => {
  message.info(`编辑API密钥: ${record.name}`)
}

const testApiKey = (record) => {
  if (record.type === 'SP-API') {
    testSpApi()
  } else {
    testAdsApi()
  }
}

const deleteApiKey = (record) => {
  message.success(`删除API密钥: ${record.name}`)
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.amazon-config-container {
  padding: 24px;
  
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
  
  .config-cards {
    margin-bottom: 24px;
    
    .config-card {
      .config-item {
        margin-bottom: 16px;
        
        .config-label {
          margin-left: 8px;
        }
      }
      
      .config-actions {
        .ant-btn {
          margin-right: 8px;
        }
      }
    }
  }
  
  .api-keys-card,
  .test-results-card {
    margin-bottom: 24px;
  }
  
  .test-result-item {
    .test-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      
      .test-api {
        font-weight: 500;
      }
      
      .test-time {
        color: #666;
        font-size: 12px;
      }
    }
    
    .test-message {
      margin-bottom: 8px;
    }
    
    .test-data {
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      
      pre {
        margin: 0;
        font-size: 12px;
      }
    }
  }
}
</style>
