<template>
  <div class="system-monitor-container">
    <div class="page-header">
      <h1>系统监控</h1>
      <p>实时监控系统状态、性能指标和服务健康度</p>
    </div>

    <!-- 系统状态概览 -->
    <div class="status-overview">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic
              title="系统状态"
              :value="systemStatus"
              :value-style="{ color: systemStatus === '正常' ? '#3f8600' : '#cf1322' }"
            />
            <div class="status-indicator">
              <a-badge :status="systemStatus === '正常' ? 'success' : 'error'" />
              <span>{{ systemUptime }}</span>
            </div>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic
              title="CPU使用率"
              :value="cpuUsage"
              suffix="%"
              :value-style="{ color: cpuUsage > 80 ? '#cf1322' : '#3f8600' }"
            />
            <div class="progress-bar">
              <a-progress :percent="cpuUsage" :show-info="false" size="small" />
            </div>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic
              title="内存使用率"
              :value="memoryUsage"
              suffix="%"
              :value-style="{ color: memoryUsage > 85 ? '#cf1322' : '#3f8600' }"
            />
            <div class="progress-bar">
              <a-progress :percent="memoryUsage" :show-info="false" size="small" />
            </div>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic
              title="活跃用户"
              :value="activeUsers"
              suffix="人"
              :value-style="{ color: '#1890ff' }"
            />
            <div class="status-indicator">
              <span>在线用户数</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 服务状态 -->
    <a-card title="服务状态" class="services-card">
      <a-row :gutter="16">
        <a-col :span="8" v-for="service in services" :key="service.name">
          <div class="service-item">
            <div class="service-header">
              <a-badge :status="service.status === 'running' ? 'success' : 'error'" />
              <span class="service-name">{{ service.name }}</span>
              <span class="service-version">v{{ service.version }}</span>
            </div>
            <div class="service-details">
              <div>端口: {{ service.port }}</div>
              <div>响应时间: {{ service.responseTime }}ms</div>
              <div>最后检查: {{ service.lastCheck }}</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 实时日志 -->
    <a-card title="实时日志" class="logs-card">
      <template #extra>
        <a-space>
          <a-select v-model:value="logLevel" style="width: 120px">
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="error">错误</a-select-option>
            <a-select-option value="warn">警告</a-select-option>
            <a-select-option value="info">信息</a-select-option>
          </a-select>
          <a-button @click="clearLogs">清空日志</a-button>
          <a-button @click="toggleAutoRefresh">
            {{ autoRefresh ? '停止' : '开始' }}自动刷新
          </a-button>
        </a-space>
      </template>

      <div class="logs-container">
        <div 
          v-for="log in filteredLogs" 
          :key="log.id"
          :class="['log-item', `log-${log.level}`]"
        >
          <span class="log-time">{{ log.timestamp }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </a-card>

    <!-- 性能图表 -->
    <a-row :gutter="24">
      <a-col :span="12">
        <a-card title="CPU & 内存使用趋势" class="chart-card">
          <div id="performance-chart" style="height: 300px;"></div>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card title="API请求统计" class="chart-card">
          <div id="api-chart" style="height: 300px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 数据库连接状态 -->
    <a-card title="数据库连接" class="database-card">
      <a-row :gutter="16">
        <a-col :span="8" v-for="db in databases" :key="db.name">
          <div class="database-item">
            <div class="database-header">
              <a-badge :status="db.status === 'connected' ? 'success' : 'error'" />
              <span class="database-name">{{ db.name }}</span>
            </div>
            <div class="database-details">
              <div>主机: {{ db.host }}</div>
              <div>连接数: {{ db.connections }}/{{ db.maxConnections }}</div>
              <div>响应时间: {{ db.responseTime }}ms</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { computed, onMounted, onUnmounted, ref } from 'vue'

// 响应式数据
const systemStatus = ref('正常')
const systemUptime = ref('运行时间: 15天 8小时')
const cpuUsage = ref(45)
const memoryUsage = ref(68)
const activeUsers = ref(23)
const logLevel = ref('all')
const autoRefresh = ref(true)

const services = ref([
  {
    name: 'Django Backend',
    version: '4.2.0',
    status: 'running',
    port: 8000,
    responseTime: 45,
    lastCheck: '刚刚'
  },
  {
    name: 'Vue Frontend',
    version: '3.3.0',
    status: 'running',
    port: 3000,
    responseTime: 12,
    lastCheck: '刚刚'
  },
  {
    name: 'Redis Cache',
    version: '7.0.0',
    status: 'running',
    port: 6379,
    responseTime: 2,
    lastCheck: '刚刚'
  },
  {
    name: 'Celery Worker',
    version: '5.3.0',
    status: 'error',
    port: '-',
    responseTime: 0,
    lastCheck: '2分钟前'
  }
])

const databases = ref([
  {
    name: 'PostgreSQL主库',
    host: 'localhost:5432',
    status: 'connected',
    connections: 15,
    maxConnections: 100,
    responseTime: 8
  },
  {
    name: 'PostgreSQL从库',
    host: 'localhost:5433',
    status: 'connected',
    connections: 5,
    maxConnections: 50,
    responseTime: 12
  }
])

const logs = ref([
  {
    id: 1,
    timestamp: '2024-01-15 14:30:25',
    level: 'info',
    message: '用户 developer 登录系统'
  },
  {
    id: 2,
    timestamp: '2024-01-15 14:30:20',
    level: 'warn',
    message: 'Redis连接池使用率超过80%'
  },
  {
    id: 3,
    timestamp: '2024-01-15 14:30:15',
    level: 'error',
    message: 'Amazon API调用失败: 访问令牌过期'
  },
  {
    id: 4,
    timestamp: '2024-01-15 14:30:10',
    level: 'info',
    message: '定时任务执行完成: 同步库存数据'
  }
])

// 计算属性
const filteredLogs = computed(() => {
  if (logLevel.value === 'all') {
    return logs.value
  }
  return logs.value.filter(log => log.level === logLevel.value)
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const clearLogs = () => {
  logs.value = []
  message.success('日志已清空')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    // 模拟数据更新
    cpuUsage.value = Math.floor(Math.random() * 30) + 40
    memoryUsage.value = Math.floor(Math.random() * 20) + 60
    activeUsers.value = Math.floor(Math.random() * 10) + 20
    
    // 添加新日志
    const newLog = {
      id: Date.now(),
      timestamp: new Date().toLocaleString(),
      level: ['info', 'warn', 'error'][Math.floor(Math.random() * 3)],
      message: `系统监控数据更新 - CPU: ${cpuUsage.value}%, 内存: ${memoryUsage.value}%`
    }
    logs.value.unshift(newLog)
    
    // 保持日志数量在合理范围
    if (logs.value.length > 50) {
      logs.value = logs.value.slice(0, 50)
    }
  }, 5000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.system-monitor-container {
  padding: 24px;
  
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
  
  .status-overview {
    margin-bottom: 24px;
    
    .status-card {
      text-align: center;
      
      .status-indicator {
        margin-top: 8px;
        font-size: 12px;
        color: #666;
      }
      
      .progress-bar {
        margin-top: 8px;
      }
    }
  }
  
  .services-card,
  .logs-card,
  .database-card {
    margin-bottom: 24px;
  }
  
  .service-item,
  .database-item {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    margin-bottom: 16px;
    
    .service-header,
    .database-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .service-name,
      .database-name {
        margin-left: 8px;
        font-weight: 500;
      }
      
      .service-version {
        margin-left: auto;
        color: #666;
        font-size: 12px;
      }
    }
    
    .service-details,
    .database-details {
      font-size: 12px;
      color: #666;
      
      div {
        margin-bottom: 4px;
      }
    }
  }
  
  .logs-container {
    max-height: 400px;
    overflow-y: auto;
    
    .log-item {
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      
      .log-time {
        color: #666;
        margin-right: 12px;
      }
      
      .log-level {
        margin-right: 12px;
        font-weight: bold;
      }
      
      &.log-error {
        background-color: #fff2f0;
        .log-level { color: #cf1322; }
      }
      
      &.log-warn {
        background-color: #fffbe6;
        .log-level { color: #d48806; }
      }
      
      &.log-info {
        background-color: #f6ffed;
        .log-level { color: #389e0d; }
      }
    }
  }
  
  .chart-card {
    margin-bottom: 24px;
  }
}
</style>
