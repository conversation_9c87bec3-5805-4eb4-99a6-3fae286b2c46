<template>
  <div class="system-monitor-container">
    <div class="page-header">
      <h1>系统监控</h1>
      <p>实时监控系统状态、性能指标和服务健康度</p>
    </div>

    <!-- 系统状态概览 -->
    <div class="status-overview">
      <ElRow :gutter="24">
        <ElCol :span="6">
          <ElCard class="status-card">
            <div class="statistic">
              <div class="statistic-title">系统状态</div>
              <div class="statistic-value" :style="{ color: systemStatus === '正常' ? '#3f8600' : '#cf1322' }">
                {{ systemStatus }}
              </div>
            </div>
            <div class="status-indicator">
              <ElBadge :type="systemStatus === '正常' ? 'success' : 'danger'" :is-dot="true" />
              <span>{{ systemUptime }}</span>
            </div>
          </ElCard>
        </ElCol>

        <ElCol :span="6">
          <ElCard class="status-card">
            <div class="statistic">
              <div class="statistic-title">CPU使用率</div>
              <div class="statistic-value" :style="{ color: cpuUsage > 80 ? '#cf1322' : '#3f8600' }">
                {{ cpuUsage }}%
              </div>
            </div>
            <div class="progress-bar">
              <ElProgress :percentage="cpuUsage" :show-text="false" size="small" />
            </div>
          </ElCard>
        </ElCol>

        <ElCol :span="6">
          <ElCard class="status-card">
            <div class="statistic">
              <div class="statistic-title">内存使用率</div>
              <div class="statistic-value" :style="{ color: memoryUsage > 85 ? '#cf1322' : '#3f8600' }">
                {{ memoryUsage }}%
              </div>
            </div>
            <div class="progress-bar">
              <ElProgress :percentage="memoryUsage" :show-text="false" size="small" />
            </div>
          </ElCard>
        </ElCol>

        <ElCol :span="6">
          <ElCard class="status-card">
            <div class="statistic">
              <div class="statistic-title">活跃用户</div>
              <div class="statistic-value" style="color: #1890ff">
                {{ activeUsers }}人
              </div>
            </div>
            <div class="status-indicator">
              <span>在线用户数</span>
            </div>
          </ElCard>
        </ElCol>
      </ElRow>
    </div>

    <!-- 服务状态 -->
    <ElCard class="services-card">
      <template #header>
        <span>服务状态</span>
      </template>
      <ElRow :gutter="16">
        <ElCol :span="8" v-for="service in services" :key="service.name">
          <div class="service-item">
            <div class="service-header">
              <ElBadge :type="service.status === 'running' ? 'success' : 'danger'" :is-dot="true" />
              <span class="service-name">{{ service.name }}</span>
              <span class="service-version">v{{ service.version }}</span>
            </div>
            <div class="service-details">
              <div>端口: {{ service.port }}</div>
              <div>响应时间: {{ service.responseTime }}ms</div>
              <div>最后检查: {{ service.lastCheck }}</div>
            </div>
          </div>
        </ElCol>
      </ElRow>
    </ElCard>

    <!-- 实时日志 -->
    <ElCard class="logs-card">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>实时日志</span>
          <div style="display: flex; gap: 8px;">
            <ElSelect v-model="logLevel" style="width: 120px">
              <ElOption value="all" label="全部" />
              <ElOption value="error" label="错误" />
              <ElOption value="warn" label="警告" />
              <ElOption value="info" label="信息" />
            </ElSelect>
            <ElButton @click="clearLogs">清空日志</ElButton>
            <ElButton @click="toggleAutoRefresh">
              {{ autoRefresh ? '停止' : '开始' }}自动刷新
            </ElButton>
          </div>
        </div>
      </template>

      <div class="logs-container">
        <div 
          v-for="log in filteredLogs" 
          :key="log.id"
          :class="['log-item', `log-${log.level}`]"
        >
          <span class="log-time">{{ log.timestamp }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </ElCard>

    <!-- 性能图表 -->
    <ElRow :gutter="24">
      <ElCol :span="12">
        <ElCard class="chart-card">
          <template #header>
            <span>CPU & 内存使用趋势</span>
          </template>
          <div id="performance-chart" style="height: 300px;"></div>
        </ElCard>
      </ElCol>

      <ElCol :span="12">
        <ElCard class="chart-card">
          <template #header>
            <span>API请求统计</span>
          </template>
          <div id="api-chart" style="height: 300px;"></div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 数据库连接状态 -->
    <ElCard class="database-card">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>数据库连接</span>
          <ElButton size="small" @click="fetchDatabaseStatus">刷新状态</ElButton>
        </div>
      </template>
      <ElRow :gutter="16">
        <ElCol :span="8" v-for="db in databases" :key="db.name">
          <div class="database-item">
            <div class="database-header">
              <ElBadge :type="db.status === 'connected' ? 'success' : 'danger'" :is-dot="true" />
              <span class="database-name">{{ db.name }}</span>
            </div>
            <div class="database-details">
              <div>主机: {{ db.host }}</div>
              <div>连接数: {{ db.connections }}/{{ db.maxConnections }}</div>
              <div>响应时间: {{ db.responseTime }}ms</div>
            </div>
          </div>
        </ElCol>
      </ElRow>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { TestDataService } from '@/api/businessApi'
import { ElMessage } from 'element-plus'
import { computed, onMounted, onUnmounted, ref } from 'vue'

// 响应式数据
const systemStatus = ref('正常')
const systemUptime = ref('运行时间: 15天 8小时')
const cpuUsage = ref(45)
const memoryUsage = ref(68)
const activeUsers = ref(23)
const logLevel = ref('all')
const autoRefresh = ref(true)

const services = ref([
  {
    name: 'Django Backend',
    version: '4.2.0',
    status: 'running',
    port: 8000,
    responseTime: 45,
    lastCheck: '刚刚'
  },
  {
    name: 'Vue Frontend',
    version: '3.3.0',
    status: 'running',
    port: 3000,
    responseTime: 12,
    lastCheck: '刚刚'
  },
  {
    name: 'Redis Cache',
    version: '7.0.0',
    status: 'running',
    port: 6379,
    responseTime: 2,
    lastCheck: '刚刚'
  },
  {
    name: 'Celery Worker',
    version: '5.3.0',
    status: 'error',
    port: '-',
    responseTime: 0,
    lastCheck: '2分钟前'
  }
])

const databases = ref([
  {
    name: 'MySQL主库',
    host: 'localhost:3306',
    status: 'connected',
    connections: 12,
    maxConnections: 150,
    responseTime: 5
  },
  {
    name: 'MySQL从库',
    host: 'localhost:3307',
    status: 'connected',
    connections: 8,
    maxConnections: 100,
    responseTime: 7
  }
])

// 获取真实数据库状态
const fetchDatabaseStatus = async () => {
  try {
    const response = await TestDataService.getDatabaseStatus()
    console.log('🔍 数据库状态:', response)

    // 将后端数据转换为前端显示格式
    if (response.databases) {
      const realDatabases = Object.entries(response.databases).map(([dbAlias, dbInfo]: [string, any]) => ({
        name: `${dbInfo.database_name} (${dbAlias})`,
        host: dbAlias.includes('mpr') ? 'localhost:3306' :
              dbAlias.includes('rl') ? 'localhost:3307' :
              dbAlias.includes('eo') ? 'localhost:3308' :
              dbAlias.includes('zz') ? 'localhost:3309' :
              dbAlias.includes('wh') ? 'localhost:3310' : 'localhost:3306',
        status: dbInfo.status === 'connected' ? 'connected' : 'error',
        connections: Math.floor(Math.random() * 20) + 5, // 模拟连接数
        maxConnections: 150,
        responseTime: Math.floor(Math.random() * 10) + 3 // 模拟响应时间
      }))

      databases.value = realDatabases
      ElMessage.success(`已获取 ${realDatabases.length} 个数据库的状态信息`)
    }
  } catch (error: any) {
    console.error('❌ 获取数据库状态失败:', error)

    // 检查是否是认证错误
    if (error?.response?.status === 401) {
      ElMessage.warning('请先登录系统后再查看真实数据库状态')
    } else if (error?.response?.status === 403) {
      ElMessage.warning('权限不足，无法访问数据库状态信息')
    } else {
      ElMessage.warning('获取数据库状态失败，显示模拟数据')
    }
  }
}

const logs = ref([
  {
    id: 1,
    timestamp: '2024-01-15 14:30:25',
    level: 'info',
    message: '用户 developer 登录系统'
  },
  {
    id: 2,
    timestamp: '2024-01-15 14:30:20',
    level: 'warn',
    message: 'Redis连接池使用率超过80%'
  },
  {
    id: 3,
    timestamp: '2024-01-15 14:30:15',
    level: 'error',
    message: 'Amazon API调用失败: 访问令牌过期'
  },
  {
    id: 4,
    timestamp: '2024-01-15 14:30:10',
    level: 'info',
    message: '定时任务执行完成: 同步库存数据'
  }
])

// 计算属性
const filteredLogs = computed(() => {
  if (logLevel.value === 'all') {
    return logs.value
  }
  return logs.value.filter(log => log.level === logLevel.value)
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    // 模拟数据更新
    cpuUsage.value = Math.floor(Math.random() * 30) + 40
    memoryUsage.value = Math.floor(Math.random() * 20) + 60
    activeUsers.value = Math.floor(Math.random() * 10) + 20
    
    // 添加新日志
    const newLog = {
      id: Date.now(),
      timestamp: new Date().toLocaleString(),
      level: ['info', 'warn', 'error'][Math.floor(Math.random() * 3)],
      message: `系统监控数据更新 - CPU: ${cpuUsage.value}%, 内存: ${memoryUsage.value}%`
    }
    logs.value.unshift(newLog)
    
    // 保持日志数量在合理范围
    if (logs.value.length > 50) {
      logs.value = logs.value.slice(0, 50)
    }
  }, 5000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  if (autoRefresh.value) {
    startAutoRefresh()
  }
  // 获取真实数据库状态
  fetchDatabaseStatus()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.system-monitor-container {
  padding: 24px;
  
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
  
  .status-overview {
    margin-bottom: 24px;

    .status-card {
      text-align: center;

      .statistic {
        margin-bottom: 16px;

        .statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .statistic-value {
          font-size: 24px;
          font-weight: 500;
          line-height: 1.2;
        }
      }

      .status-indicator {
        margin-top: 8px;
        font-size: 12px;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }

      .progress-bar {
        margin-top: 8px;
      }
    }
  }
  
  .services-card,
  .logs-card,
  .database-card {
    margin-bottom: 24px;
  }
  
  .service-item,
  .database-item {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    margin-bottom: 16px;
    
    .service-header,
    .database-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .service-name,
      .database-name {
        margin-left: 8px;
        font-weight: 500;
      }
      
      .service-version {
        margin-left: auto;
        color: #666;
        font-size: 12px;
      }
    }
    
    .service-details,
    .database-details {
      font-size: 12px;
      color: #666;
      
      div {
        margin-bottom: 4px;
      }
    }
  }
  
  .logs-container {
    max-height: 400px;
    overflow-y: auto;
    
    .log-item {
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      
      .log-time {
        color: #666;
        margin-right: 12px;
      }
      
      .log-level {
        margin-right: 12px;
        font-weight: bold;
      }
      
      &.log-error {
        background-color: #fff2f0;
        .log-level { color: #cf1322; }
      }
      
      &.log-warn {
        background-color: #fffbe6;
        .log-level { color: #d48806; }
      }
      
      &.log-info {
        background-color: #f6ffed;
        .log-level { color: #389e0d; }
      }
    }
  }
  
  .chart-card {
    margin-bottom: 24px;
  }
}
</style>
