<template>
  <div class="database-tools-container">
    <div class="page-header">
      <h1>数据库工具</h1>
      <p>SQL查询、数据导入导出、数据库结构查看和管理</p>
    </div>

    <!-- 工具栏 -->
    <a-card class="toolbar-card">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select v-model:value="selectedDatabase" placeholder="选择数据库" style="width: 100%">
            <a-select-option value="default">主数据库 (PostgreSQL)</a-select-option>
            <a-select-option value="cache">缓存数据库 (Redis)</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="executeQuery" :loading="queryLoading">
            <template #icon><VideoPlay /></template>
            执行查询
          </a-button>
        </a-col>
        <a-col :span="6">
          <a-button @click="clearQuery">
            <template #icon><Delete /></template>
            清空
          </a-button>
        </a-col>
        <a-col :span="6">
          <a-button @click="showTableStructure">
            <template #icon><TableOutlined /></template>
            表结构
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- SQL编辑器 -->
    <a-card title="SQL查询编辑器" class="sql-editor-card">
      <template #extra>
        <a-space>
          <a-button size="small" @click="insertCommonQuery('users')">查询用户</a-button>
          <a-button size="small" @click="insertCommonQuery('menus')">查询菜单</a-button>
          <a-button size="small" @click="insertCommonQuery('permissions')">查询权限</a-button>
        </a-space>
      </template>

      <div class="sql-editor">
        <a-textarea
          v-model:value="sqlQuery"
          placeholder="输入SQL查询语句..."
          :rows="8"
          style="font-family: 'Courier New', monospace;"
        />
      </div>
    </a-card>

    <!-- 查询结果 -->
    <a-card v-if="queryResults.length > 0 || queryError" title="查询结果" class="results-card">
      <template #extra>
        <a-space>
          <span>执行时间: {{ executionTime }}ms</span>
          <a-button size="small" @click="exportResults" :disabled="queryResults.length === 0">
            <template #icon><DownloadOutlined /></template>
            导出
          </a-button>
        </a-space>
      </template>

      <!-- 错误信息 -->
      <a-alert
        v-if="queryError"
        :message="queryError"
        type="error"
        show-icon
        style="margin-bottom: 16px;"
      />

      <!-- 结果表格 -->
      <a-table
        v-if="queryResults.length > 0"
        :columns="resultColumns"
        :data-source="queryResults"
        :pagination="{ pageSize: 50 }"
        :scroll="{ x: 'max-content' }"
        size="small"
        bordered
      />
    </a-card>

    <!-- 数据库表结构 -->
    <a-modal
      v-model:open="showTableModal"
      title="数据库表结构"
      width="800px"
      :footer="null"
    >
      <a-tabs v-model:activeKey="activeTableTab">
        <a-tab-pane key="tables" tab="表列表">
          <a-table
            :columns="tableColumns"
            :data-source="tables"
            :pagination="false"
            size="small"
            @row-click="selectTable"
          />
        </a-tab-pane>
        
        <a-tab-pane key="structure" tab="表结构" :disabled="!selectedTable">
          <div v-if="selectedTable">
            <h4>{{ selectedTable }} 表结构</h4>
            <a-table
              :columns="columnStructureColumns"
              :data-source="tableStructure"
              :pagination="false"
              size="small"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 常用查询模板 -->
    <a-card title="常用查询模板" class="templates-card">
      <a-row :gutter="16">
        <a-col :span="8" v-for="template in queryTemplates" :key="template.name">
          <a-card size="small" :title="template.name" class="template-card">
            <p>{{ template.description }}</p>
            <a-button size="small" type="link" @click="useTemplate(template)">
              使用模板
            </a-button>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Delete, Grid, Download } from '@element-plus/icons-vue'

// 响应式数据
const selectedDatabase = ref('default')
const sqlQuery = ref('')
const queryLoading = ref(false)
const queryResults = ref([])
const queryError = ref('')
const executionTime = ref(0)
const resultColumns = ref([])

const showTableModal = ref(false)
const activeTableTab = ref('tables')
const selectedTable = ref('')
const tables = ref([])
const tableStructure = ref([])

// 表格列配置
const tableColumns = [
  { title: '表名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '记录数', dataIndex: 'rows', key: 'rows' },
  { title: '大小', dataIndex: 'size', key: 'size' },
  { title: '注释', dataIndex: 'comment', key: 'comment' }
]

const columnStructureColumns = [
  { title: '字段名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '长度', dataIndex: 'length', key: 'length' },
  { title: '允许空值', dataIndex: 'nullable', key: 'nullable' },
  { title: '默认值', dataIndex: 'default', key: 'default' },
  { title: '注释', dataIndex: 'comment', key: 'comment' }
]

// 查询模板
const queryTemplates = ref([
  {
    name: '用户统计',
    description: '查询各角色用户数量',
    sql: `SELECT role, COUNT(*) as count 
FROM core_customuser 
WHERE is_active = true 
GROUP BY role 
ORDER BY count DESC;`
  },
  {
    name: '菜单权限',
    description: '查询菜单和权限关联',
    sql: `SELECT m.name as menu_name, m.path, COUNT(c.id) as children_count
FROM core_menu m
LEFT JOIN core_menu c ON c.parent_id = m.id
WHERE m.parent_id IS NULL
GROUP BY m.id, m.name, m.path
ORDER BY m.name;`
  },
  {
    name: '权限分布',
    description: '查询权限分类统计',
    sql: `SELECT category, COUNT(*) as permission_count
FROM core_permission
GROUP BY category
ORDER BY permission_count DESC;`
  },
  {
    name: '用户权限',
    description: '查询用户权限分配情况',
    sql: `SELECT u.username, u.role, COUNT(up.permission_id) as permission_count
FROM core_customuser u
LEFT JOIN core_userpermission up ON up.user_id = u.id
WHERE u.is_active = true
GROUP BY u.id, u.username, u.role
ORDER BY permission_count DESC;`
  },
  {
    name: '系统日志',
    description: '查询最近的系统操作日志',
    sql: `SELECT * FROM django_admin_log 
ORDER BY action_time DESC 
LIMIT 20;`
  },
  {
    name: '数据库大小',
    description: '查询各表的大小统计',
    sql: `SELECT 
  schemaname,
  tablename,
  attname,
  n_distinct,
  correlation
FROM pg_stats
WHERE schemaname = 'public'
ORDER BY tablename;`
  }
])

// 方法
const executeQuery = async () => {
  if (!sqlQuery.value.trim()) {
    message.warning('请输入SQL查询语句')
    return
  }

  queryLoading.value = true
  queryError.value = ''
  
  try {
    const startTime = Date.now()
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟查询结果
    const mockResults = [
      { id: 1, username: 'developer', role: 'SUPER_ADMIN', is_active: true, date_joined: '2024-01-15' },
      { id: 2, username: 'MM', role: 'MEMBER', is_active: true, date_joined: '2024-01-10' },
      { id: 3, username: 'mpr_admin', role: 'REGION_ADMIN', is_active: true, date_joined: '2024-01-08' }
    ]
    
    queryResults.value = mockResults
    executionTime.value = Date.now() - startTime
    
    // 动态生成列配置
    if (mockResults.length > 0) {
      resultColumns.value = Object.keys(mockResults[0]).map(key => ({
        title: key,
        dataIndex: key,
        key: key,
        width: 150
      }))
    }
    
    message.success(`查询完成，返回 ${mockResults.length} 条记录`)
    
  } catch (error) {
    queryError.value = error.message || '查询执行失败'
    message.error('查询执行失败')
  } finally {
    queryLoading.value = false
  }
}

const clearQuery = () => {
  sqlQuery.value = ''
  queryResults.value = []
  queryError.value = ''
  resultColumns.value = []
}

const insertCommonQuery = (type: string) => {
  const queries = {
    users: 'SELECT * FROM core_customuser WHERE is_active = true ORDER BY date_joined DESC LIMIT 10;',
    menus: 'SELECT * FROM core_menu WHERE parent_id IS NULL ORDER BY name;',
    permissions: 'SELECT * FROM core_permission ORDER BY category, name;'
  }
  
  sqlQuery.value = queries[type] || ''
}

const showTableStructure = () => {
  // 模拟表数据
  tables.value = [
    { name: 'core_customuser', type: 'table', rows: 15, size: '8KB', comment: '用户表' },
    { name: 'core_menu', type: 'table', rows: 45, size: '12KB', comment: '菜单表' },
    { name: 'core_permission', type: 'table', rows: 32, size: '6KB', comment: '权限表' },
    { name: 'core_userpermission', type: 'table', rows: 128, size: '4KB', comment: '用户权限关联表' }
  ]
  
  showTableModal.value = true
}

const selectTable = (record) => {
  selectedTable.value = record.name
  activeTableTab.value = 'structure'
  
  // 模拟表结构数据
  tableStructure.value = [
    { name: 'id', type: 'integer', length: '', nullable: false, default: 'nextval(...)', comment: '主键' },
    { name: 'username', type: 'varchar', length: '150', nullable: false, default: '', comment: '用户名' },
    { name: 'email', type: 'varchar', length: '254', nullable: true, default: '', comment: '邮箱' },
    { name: 'is_active', type: 'boolean', length: '', nullable: false, default: 'true', comment: '是否激活' }
  ]
}

const useTemplate = (template) => {
  sqlQuery.value = template.sql
  message.success(`已加载模板: ${template.name}`)
}

const exportResults = () => {
  if (queryResults.value.length === 0) return
  
  // 简单的CSV导出
  const csv = [
    resultColumns.value.map(col => col.title).join(','),
    ...queryResults.value.map(row => 
      resultColumns.value.map(col => row[col.dataIndex]).join(',')
    )
  ].join('\n')
  
  const blob = new Blob([csv], { type: 'text/csv' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `query_results_${Date.now()}.csv`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('查询结果已导出')
}
</script>

<style lang="scss" scoped>
.database-tools-container {
  padding: 24px;
  
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
  
  .toolbar-card,
  .sql-editor-card,
  .results-card,
  .templates-card {
    margin-bottom: 24px;
  }
  
  .sql-editor {
    .ant-input {
      font-family: 'Courier New', monospace;
      font-size: 14px;
    }
  }
  
  .template-card {
    height: 120px;
    
    p {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
    }
  }
}
</style>
