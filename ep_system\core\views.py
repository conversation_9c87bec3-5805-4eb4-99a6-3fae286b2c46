from django.shortcuts import render
from django.contrib.auth import authenticate
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import UserRegistrationSerializer, UserDetailSerializer, UserCreateUpdateSerializer
from .models import CustomUser, MemberProfile, AssistantProfile, Menu, Permission, UserPermission
from .middleware import get_current_region, get_current_domain_info
import logging

logger = logging.getLogger(__name__)

# Create your views here.

class RegistrationAPIView(generics.CreateAPIView):
    """
    用户注册API视图
    
    支持区域感知的用户注册功能：
    - 自动关联请求头中X-Region-Code对应的区域
    - 默认角色为MEMBER（普通会员）
    - 在当前区域内验证用户名和邮箱的唯一性
    - 不需要认证即可访问
    """
    
    queryset = CustomUser.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]  # 允许未认证用户访问
    
    def create(self, request, *args, **kwargs):
        """处理用户注册请求"""
        
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            
            return Response({
                'success': True,
                'message': '用户注册成功',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': '注册失败，请检查输入信息',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def get_serializer_context(self):
        """为序列化器提供上下文信息"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class LoginAPIView(APIView):
    """
    用户登录API视图

    接收用户名和密码，返回JWT token
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """处理分区域登录请求"""
        import logging
        logger = logging.getLogger('core')
        
        logger.info(f"登录请求 - 原始数据: {request.data}")
        logger.info(f"登录请求 - Content-Type: {request.content_type}")
        logger.info(f"登录请求 - Headers: {dict(request.headers)}")

        username = request.data.get('userName')  # 前端发送的是userName
        password = request.data.get('password')

        logger.info(f"登录请求 - 用户名: {username}, 密码长度: {len(password) if password else 0}")

        if not username or not password:
            logger.warning(f"登录失败 - 缺少必要字段: username={username}, password={'存在' if password else '不存在'}")
            return Response({
                'code': 400,
                'msg': '用户名和密码不能为空',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 直接查询用户，绕过区域限制，使用unfiltered查询
        try:
            # 使用unfiltered()方法绕过RegionAwareManager的过滤
            user = CustomUser.objects.unfiltered().get(username=username)

            # 检查用户是否被禁用
            if not user.is_active:
                logger.warning(f"登录失败 - 用户已被禁用: {username}")
                return Response({
                    'code': 401,
                    'msg': '用户账号已被禁用，请联系管理员',
                    'data': None
                }, status=status.HTTP_401_UNAUTHORIZED)

            if user.check_password(password):
                logger.info(f"密码验证成功 - 用户: {username}")

                # 生成JWT token
                try:
                    refresh = RefreshToken.for_user(user)
                    logger.info(f"JWT token生成成功 - 用户: {username}")
                except Exception as e:
                    logger.error(f"JWT token生成失败 - 用户: {username}, 错误: {str(e)}")
                    raise

                # 构建用户信息
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'role': user.role,
                    'roleDisplay': user.get_role_display(),
                }
                logger.info(f"基础用户信息构建完成 - 用户: {username}, 角色: {user.role}")

                # 添加区域信息
                if user.region:
                    user_data.update({
                        'regionId': user.region.id,
                        'regionName': user.region.name,
                        'regionCode': user.region.code,
                    })
                else:
                    # 超级管理员没有区域限制
                    user_data.update({
                        'regionId': None,
                        'regionName': '全局管理',
                        'regionCode': 'SUPER',
                    })

                return Response({
                    'code': 200,
                    'msg': '登录成功',
                    'data': {
                        'token': f'Bearer {str(refresh.access_token)}',
                        'refreshToken': str(refresh),
                        'userInfo': user_data
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'code': 401,
                    'msg': '用户名或密码错误',
                    'data': None
                }, status=status.HTTP_401_UNAUTHORIZED)
        except CustomUser.DoesNotExist:
            logger.warning(f"登录失败 - 用户不存在: {username}")
            return Response({
                'code': 401,
                'msg': '用户名或密码错误',
                'data': None
            }, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            logger.error(f"登录异常 - 用户: {username}, 错误: {str(e)}", exc_info=True)
            return Response({
                'code': 500,
                'msg': '服务器内部错误',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


from .serializers import UserRegistrationSerializer, UserDetailSerializer, UserCreateUpdateSerializer, MenuSerializer
from .models import CustomUser, MemberProfile, AssistantProfile, Menu

class MenuAPIView(APIView):
    """
    动态菜单API视图
    根据用户角色返回对应的菜单配置
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户菜单 - 根据用户权限过滤"""
        current_user = request.user
        logger.info(f"获取菜单请求 - 用户: {current_user.username}, 角色: {current_user.role}")

        # 获取所有菜单
        all_menus = Menu.objects.using('default').filter(parent__isnull=True).prefetch_related('children')

        # 根据用户权限过滤菜单
        filtered_menus = self._filter_menus_by_user_permissions(all_menus, current_user)

        serializer = MenuSerializer(filtered_menus, many=True)

        logger.info(f"菜单过滤完成 - 用户: {current_user.username}, 可访问菜单数: {len(filtered_menus)}")

        return Response({
            'code': 200,
            'msg': '获取菜单成功',
            'data': {
                'menuList': serializer.data
            }
        }, status=status.HTTP_200_OK)

    def _filter_menus_by_user_permissions(self, menus, user):
        """根据用户权限过滤菜单"""
        # 超级管理员可以看到所有菜单
        if user.role == CustomUser.Role.SUPER_ADMIN:
            return menus

        # 获取用户权限
        user_permissions = self._get_user_permissions(user)

        # 过滤菜单
        filtered_menus = []
        for menu in menus:
            if self._can_access_menu(menu, user, user_permissions):
                # 创建菜单副本并过滤子菜单
                filtered_menu = menu
                if hasattr(menu, 'children') and menu.children.exists():
                    filtered_children = []
                    for child in menu.children.all():
                        if self._can_access_menu(child, user, user_permissions):
                            filtered_children.append(child)
                    # 如果有可访问的子菜单，才显示父菜单
                    if filtered_children:
                        # 这里需要动态设置children，但Django ORM不支持直接修改
                        # 我们在序列化器中处理这个逻辑
                        filtered_menus.append(menu)
                else:
                    # 没有子菜单的直接添加
                    filtered_menus.append(menu)

        return filtered_menus

    def _get_user_permissions(self, user):
        """获取用户权限代码列表"""
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT p.code
                    FROM core_permission p
                    INNER JOIN core_userpermission up ON p.id = up.permission_id
                    WHERE up.user_id = %s
                """, [user.id])

                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return []

    def _can_access_menu(self, menu, user, user_permissions):
        """检查用户是否可以访问指定菜单"""
        # 基于菜单路径的权限映射
        menu_permission_map = {
            # 仪表盘 - 所有登录用户都可以访问工作台
            '/dashboard': ['warehouse_management', 'warehouse_view', 'sku_management', 'sku_view', 'shipping_management', 'shipping_view', 'customer_management', 'system_admin', 'user_management', 'system_development'],
            # 业务管理 - 需要客户管理权限
            '/business': ['customer_management'],
            # 系统管理 - 只有超级管理员可以访问
            '/system': ['system_admin'],
            # 仓库管理 - 需要仓库相关权限
            '/warehouse': ['warehouse_management', 'warehouse_view'],
            # SKU管理 - 需要SKU相关权限
            '/sku': ['sku_management', 'sku_view'],
            # 发货管理 - 需要发货相关权限
            '/shipping': ['shipping_management', 'shipping_view'],
            # 开发者控制台 - 需要开发者权限
            '/developer': ['system_development', 'api_management', 'database_management', 'system_monitoring'],
        }

        # 获取菜单需要的权限
        required_permissions = menu_permission_map.get(menu.path, [])

        # 如果没有定义权限要求，默认允许访问（向后兼容）
        if not required_permissions:
            return True

        # 检查用户是否有任一所需权限
        return any(perm in user_permissions for perm in required_permissions)

    def post(self, request):
        """创建新菜单"""
        serializer = MenuSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'code': 200,
                'msg': '菜单创建成功',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)
        return Response({
            'code': 400,
            'msg': '菜单创建失败',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)



class UserInfoAPIView(APIView):
    """
    获取当前用户信息API视图
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取当前登录用户的信息"""
        user = request.user

        # 构建用户信息
        user_data = {
            'userId': user.id,
            'userName': user.username,
            'phone': getattr(user, 'phone', ''),
            'role': user.role,
            'roleDisplay': user.get_role_display(),
            'avatar': '',
            'roles': [user.role.lower()],  # 根据实际角色设置
            'buttons': ['add', 'edit', 'delete'],  # 简化处理
        }

        # 添加区域信息
        if user.region:
            user_data.update({
                'regionId': user.region.id,
                'regionName': user.region.name,
                'regionCode': user.region.code,
            })
        else:
            # 超级管理员没有区域限制
            user_data.update({
                'regionId': None,
                'regionName': '全局管理',
                'regionCode': 'SUPER',
            })

        return Response({
            'code': 200,
            'msg': '获取用户信息成功',
            'data': user_data
        }, status=status.HTTP_200_OK)


class UserListAPIView(APIView):
    """
    获取用户列表API视图
    """
    permission_classes = [IsAuthenticated]

    def _check_role_creation_permission(self, creator_role, target_role):
        """检查角色创建权限"""
        # 角色创建权限映射
        ROLE_CREATION_PERMISSIONS = {
            'SUPER_ADMIN': ['REGION_ADMIN', 'FINANCE_STAFF'],
            'REGION_ADMIN': ['MEMBER', 'MEMBER_ASSISTANT', 'TRANSLATOR_ASSISTANT', 'DOCUMENT_ASSISTANT', 'WAREHOUSE_MANAGER']
        }

        allowed_roles = ROLE_CREATION_PERMISSIONS.get(creator_role, [])
        return target_role in allowed_roles

    def get(self, request):
        """获取用户列表 - 支持区域感知和区域过滤"""
        current = int(request.GET.get('current', 1))
        size = int(request.GET.get('size', 10))

        # 获取搜索参数
        name = request.GET.get('name', '').strip()           # 登录名搜索
        role = request.GET.get('role', '').strip()           # 角色搜索
        is_active = request.GET.get('isActive', '').strip()  # 状态搜索
        region_filter = request.GET.get('region', '').strip()  # 区域过滤参数

        # 计算分页
        start = (current - 1) * size
        end = start + size

        # 根据当前用户角色决定查询范围
        current_user = request.user
        if current_user.role == CustomUser.Role.SUPER_ADMIN:
            # 超级管理员可以看到所有用户（包括禁用的）
            users_query = CustomUser.objects.unfiltered().all()
            total_query = CustomUser.objects.unfiltered()

            # 超级管理员可以按区域过滤
            if region_filter:
                try:
                    from .models import Region
                    region = Region.objects.get(code=region_filter)
                    users_query = users_query.filter(region=region)
                    total_query = total_query.filter(region=region)
                except Region.DoesNotExist:
                    pass  # 如果区域不存在，忽略过滤
        else:
            # 区域管理员只能看到同区域的所有用户（包括禁用的）
            users_query = CustomUser.objects.filter(region=current_user.region)
            total_query = CustomUser.objects.filter(region=current_user.region)

        # 应用搜索过滤
        if name:
            users_query = users_query.filter(username__icontains=name)
            total_query = total_query.filter(username__icontains=name)

        if role:
            users_query = users_query.filter(role=role)
            total_query = total_query.filter(role=role)

        if is_active:
            # 将字符串转换为布尔值
            active_bool = is_active.lower() == 'true'
            users_query = users_query.filter(is_active=active_bool)
            total_query = total_query.filter(is_active=active_bool)

        # 获取分页数据
        users = users_query[start:end]
        total = total_query.count()

        user_list = []
        for user in users:
            user_data = {
                'id': user.id,
                'avatar': '',
                'createBy': 'system',
                'createTime': user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                'updateBy': 'system',
                'updateTime': user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                'status': '1',  # 在线状态
                'userName': user.username,
                'userGender': '未知',
                'nickName': user.username,
                'userPhone': getattr(user, 'phone', ''),
                'userRoles': [user.role.lower()],
                'role': user.role,
                'roleDisplay': user.get_role_display(),
                'isActive': user.is_active,  # 添加账户活跃状态字段
                'lastLoginTime': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else None,
                'lastLoginIP': None,  # 暂时为None，需要额外字段支持
                'mwsSwitch': getattr(user, 'mws_switch', False),  # MWS开关状态
                'hasRefreshToken': bool(getattr(user, 'refresh_token', None)),  # 是否有刷新令牌
            }

            # 添加Profile数据
            if user.role == CustomUser.Role.MEMBER:
                if hasattr(user, 'member_profile'):
                    profile = user.member_profile
                    user_data['memberProfile'] = {
                        'real_name': profile.real_name,
                        'english_name': profile.english_name,
                        'description': profile.description,
                        'image_server_url': profile.image_server_url,
                        'document_server_url': profile.document_server_url,
                        'sku_prefix': profile.sku_prefix,
                        'payment_channel': profile.payment_channel,
                        'parent_account': profile.parent_account,
                        'ems_vip_number': profile.ems_vip_number,
                        'ems_customer_code': profile.ems_customer_code,
                        'ems_version_info': profile.ems_version_info,
                        'international_eub_us': profile.international_eub_us,
                        'amazon_username': profile.amazon_username,
                        'amazon_seller_id': profile.amazon_seller_id,
                        'amazon_marketplace_id': profile.amazon_marketplace_id,
                        'mws_auth_token': bool(profile.mws_auth_token),  # 只返回是否存在
                        'warehouse_location': profile.warehouse_location,
                        'status': profile.status,
                        'exempt_brands': profile.exempt_brands,
                    }
                else:
                    user_data['memberProfile'] = None
            elif user.role in [
                CustomUser.Role.MEMBER_ASSISTANT,
                CustomUser.Role.TRANSLATOR_ASSISTANT,
                CustomUser.Role.DOCUMENT_ASSISTANT
            ]:
                if hasattr(user, 'assistant_profile'):
                    profile = user.assistant_profile
                    user_data['assistantProfile'] = {
                        'description': profile.description,
                        'related_finance_auth_token': bool(profile.related_finance_auth_token),
                        'internal_member_finance_auth_token': bool(profile.internal_member_finance_auth_token),
                    }
                else:
                    user_data['assistantProfile'] = None

            # 添加区域信息
            if user.region:
                user_data.update({
                    'regionId': user.region.id,
                    'regionName': user.region.name,
                    'regionCode': user.region.code,
                })
            else:
                user_data.update({
                    'regionId': None,
                    'regionName': '全局管理',
                    'regionCode': 'SUPER',
                })

            # 添加会员助理信息
            if user.role == CustomUser.Role.MEMBER and user.managed_by:
                user_data.update({
                    'managedById': user.managed_by.id,
                    'managedByName': user.managed_by.username,
                })
            else:
                user_data.update({
                    'managedById': None,
                    'managedByName': None,
                })

            user_list.append(user_data)

        return Response({
            'code': 200,
            'msg': '获取用户列表成功',
            'data': {
                'records': user_list,
                'current': current,
                'size': size,
                'total': total
            }
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """管理员创建用户 - 支持Profile数据"""
        current_user = request.user

        # 权限检查：只有管理员可以创建用户
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以创建用户',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        # 角色创建权限验证
        target_role = request.data.get('role')
        if not self._check_role_creation_permission(current_user.role, target_role):
            return Response({
                'code': 403,
                'msg': f'权限不足，您无法创建 {target_role} 角色的用户',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        # 使用新的序列化器处理数据
        serializer = UserCreateUpdateSerializer(data=request.data)

        if serializer.is_valid():
            try:
                # 创建用户（包括Profile数据）- 传递当前用户的区域信息
                if current_user.role == CustomUser.Role.SUPER_ADMIN:
                    # 超级管理员创建的用户，可以通过请求头指定区域，或使用默认区域
                    user = serializer.save()
                else:
                    # 区域管理员创建的用户，必须归属于其所在区域
                    user = serializer.save(region=current_user.region)

                # 处理权限分配
                permission_ids = request.data.get('permission_ids', [])
                permissions_assigned = 0
                if permission_ids:
                    from django.db import connection
                    for perm_id in permission_ids:
                        try:
                            with connection.cursor() as cursor:
                                cursor.execute("""
                                    INSERT INTO core_userpermission (user_id, permission_id, granted_by_id)
                                    VALUES (%s, %s, %s)
                                """, [user.id, perm_id, current_user.id])
                                permissions_assigned += 1
                        except Exception as e:
                            print(f"权限分配失败: {e}")

                # 处理助理负责会员的关联（如果是助理角色）
                responsible_member_ids = request.data.get('responsible_member_ids', [])
                members_assigned = 0
                if user.role in [
                    CustomUser.Role.MEMBER_ASSISTANT,
                    CustomUser.Role.TRANSLATOR_ASSISTANT,
                    CustomUser.Role.DOCUMENT_ASSISTANT
                ] and responsible_member_ids:
                    try:
                        # 将指定的会员设置为由该助理负责
                        members_to_update = CustomUser.objects.filter(
                            id__in=responsible_member_ids,
                            role=CustomUser.Role.MEMBER,
                            region=user.region  # 确保在同一区域
                        )
                        members_assigned = members_to_update.update(managed_by=user)
                    except Exception as e:
                        print(f"助理-会员关联失败: {e}")

                # 返回创建结果
                response_data = UserDetailSerializer(user).data
                response_data.update({
                    'defaultPassword': request.data.get('password', 'temp123456'),
                    'permissions_assigned': permissions_assigned,
                    'members_assigned': members_assigned
                })

                return Response({
                    'code': 200,
                    'msg': '用户创建成功',
                    'data': response_data
                }, status=status.HTTP_201_CREATED)

            except Exception as e:
                return Response({
                    'code': 500,
                    'msg': f'创建用户失败: {str(e)}',
                    'data': None
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response({
                'code': 400,
                'msg': '数据验证失败',
                'data': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)


class AssistantListAPIView(APIView):
    """获取助理列表API - 支持所有助理角色"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取当前区域的助理列表"""
        current_user = request.user

        # 权限检查：只有管理员可以查看助理列表
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 定义所有助理角色
            assistant_roles = [
                CustomUser.Role.MEMBER_ASSISTANT,
                CustomUser.Role.TRANSLATOR_ASSISTANT,
                CustomUser.Role.DOCUMENT_ASSISTANT
            ]

            # 获取当前区域的所有助理
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                # 超级管理员可以看到所有活跃助理
                assistants = CustomUser.objects.unfiltered().filter(
                    role__in=assistant_roles,
                    is_active=True
                )
            else:
                # 区域管理员只能看到同区域的活跃助理
                assistants = CustomUser.objects.filter(
                    role__in=assistant_roles,
                    region=current_user.region,
                    is_active=True
                )

            assistant_list = []
            for assistant in assistants:
                assistant_data = {
                    'id': assistant.id,
                    'username': assistant.username,
                    'phone': getattr(assistant, 'phone', ''),
                    'role': assistant.role,
                    'roleDisplay': assistant.get_role_display(),
                    'regionName': assistant.region.name if assistant.region else '全局',
                    'managedMemberCount': assistant.managed_members.count(),  # 负责的会员数量
                }

                # 添加助理Profile信息（如果存在）
                if hasattr(assistant, 'assistant_profile'):
                    profile = assistant.assistant_profile
                    assistant_data.update({
                        'description': profile.description,
                        'hasFinanceToken': bool(profile.related_finance_auth_token),
                        'hasInternalToken': bool(profile.internal_member_finance_auth_token),
                    })

                assistant_list.append(assistant_data)

            return Response({
                'code': 200,
                'msg': '获取助理列表成功',
                'data': assistant_list
            })

        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取助理列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DomainInfoAPIView(APIView):
    """
    域名信息API视图
    用于测试多域名支持功能
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """获取当前请求的域名和区域信息"""

        # 获取域名信息
        domain_info = get_current_domain_info()
        current_region = get_current_region()

        # 构建响应数据
        response_data = {
            'request_info': {
                'host': request.get_host(),
                'method': request.method,
                'path': request.path,
                'headers': {
                    'user-agent': request.headers.get('User-Agent', ''),
                    'x-region-code': request.headers.get('X-Region-Code', ''),
                }
            },
            'domain_info': domain_info,
            'region_info': {
                'current_region': {
                    'id': current_region.id if current_region else None,
                    'name': current_region.name if current_region else None,
                    'code': current_region.code if current_region else None,
                } if current_region else None,
                'access_type': domain_info.get('access_type') if domain_info else 'unknown'
            }
        }

        return Response({
            'code': 200,
            'msg': '获取域名信息成功',
            'data': response_data
        }, status=status.HTTP_200_OK)


class UserDetailAPIView(APIView):
    """用户详情API - 支持删除、更新等操作"""
    permission_classes = [IsAuthenticated]

    def get(self, request, user_id):
        """获取用户详情 - 支持Profile数据"""
        current_user = request.user

        try:
            # 查找用户
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                user = CustomUser.objects.unfiltered().select_related(
                    'region', 'managed_by', 'member_profile', 'assistant_profile'
                ).get(id=user_id)
            else:
                user = CustomUser.objects.select_related(
                    'region', 'managed_by', 'member_profile', 'assistant_profile'
                ).get(id=user_id, region=current_user.region)

            # 使用序列化器获取用户详情
            user_data = UserDetailSerializer(user).data

            # 获取用户权限
            user_permissions = []
            if user.role != CustomUser.Role.SUPER_ADMIN:
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT p.id, p.name, p.code, p.description, p.category
                        FROM core_permission p
                        INNER JOIN core_userpermission up ON p.id = up.permission_id
                        WHERE up.user_id = %s
                    """, [user.id])

                    for row in cursor.fetchall():
                        user_permissions.append({
                            'id': row[0],
                            'name': row[1],
                            'code': row[2],
                            'description': row[3],
                            'category': row[4]
                        })

            # 获取负责的会员（如果是助理）
            responsible_members = []
            if user.role in [
                CustomUser.Role.MEMBER_ASSISTANT,
                CustomUser.Role.TRANSLATOR_ASSISTANT,
                CustomUser.Role.DOCUMENT_ASSISTANT
            ]:
                managed_members = CustomUser.objects.filter(managed_by=user)
                responsible_members = [
                    {
                        'id': member.id,
                        'username': member.username,
                    }
                    for member in managed_members
                ]

            # 添加managedBy信息（负责该用户的助理）
            managed_by_info = None
            if user.managed_by:
                managed_by_info = {
                    'id': user.managed_by.id,
                    'username': user.managed_by.username,
                }

            # 添加额外信息到序列化器数据
            user_data.update({
                'permissions': user_permissions,
                'responsibleMembers': responsible_members,
                'managedBy': managed_by_info
            })

            return Response({
                'code': 200,
                'msg': '获取用户详情成功',
                'data': user_data
            })

        except CustomUser.DoesNotExist:
            return Response({
                'code': 404,
                'msg': '用户不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取用户详情失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, user_id):
        """更新用户信息 - 支持Profile数据"""
        current_user = request.user

        # 权限检查
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以编辑用户',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 查找要更新的用户
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                user = CustomUser.objects.unfiltered().get(id=user_id)
            else:
                user = CustomUser.objects.get(id=user_id, region=current_user.region)

            # 使用序列化器更新用户和Profile数据
            serializer = UserCreateUpdateSerializer(user, data=request.data, partial=True)

            if serializer.is_valid():
                # 保存更新
                updated_user = serializer.save()

                # 处理权限更新
                permission_ids = request.data.get('permission_ids', [])
                permissions_assigned = 0
                if updated_user.role != CustomUser.Role.SUPER_ADMIN and permission_ids:
                    from django.db import connection

                    # 删除现有权限
                    with connection.cursor() as cursor:
                        cursor.execute("DELETE FROM core_userpermission WHERE user_id = %s", [updated_user.id])

                    # 添加新权限
                    for perm_id in permission_ids:
                        try:
                            with connection.cursor() as cursor:
                                cursor.execute("""
                                    INSERT INTO core_userpermission (user_id, permission_id, granted_by_id)
                                    VALUES (%s, %s, %s)
                                """, [updated_user.id, perm_id, current_user.id])
                                permissions_assigned += 1
                        except Exception as e:
                            print(f"权限分配失败 {perm_id}: {e}")

                # 处理助理-会员关联更新
                responsible_member_ids = request.data.get('responsible_member_ids', [])
                members_assigned = 0
                if updated_user.role in [
                    CustomUser.Role.MEMBER_ASSISTANT,
                    CustomUser.Role.TRANSLATOR_ASSISTANT,
                    CustomUser.Role.DOCUMENT_ASSISTANT
                ] and responsible_member_ids:
                    # 先清理现有关联
                    CustomUser.objects.filter(managed_by=updated_user).update(managed_by=None)

                    # 建立新关联
                    for member_id in responsible_member_ids:
                        try:
                            member = CustomUser.objects.get(id=member_id, role=CustomUser.Role.MEMBER)
                            member.managed_by = updated_user
                            member.save()
                            members_assigned += 1
                        except CustomUser.DoesNotExist:
                            print(f"会员不存在: {member_id}")

                # 返回更新后的用户详情
                response_data = UserDetailSerializer(updated_user).data
                response_data.update({
                    'permissions_assigned': permissions_assigned if updated_user.role != CustomUser.Role.SUPER_ADMIN else 'all',
                    'members_assigned': members_assigned
                })

                return Response({
                    'code': 200,
                    'msg': '用户更新成功',
                    'data': response_data
                })
            else:
                return Response({
                    'code': 400,
                    'msg': '数据验证失败',
                    'data': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except CustomUser.DoesNotExist:
            return Response({
                'code': 404,
                'msg': '用户不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'更新用户失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, user_id):
        """删除用户"""
        current_user = request.user

        # 权限检查：只有管理员可以删除用户
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以删除用户',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 查找要删除的用户
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                # 超级管理员可以删除任何用户
                user_to_delete = CustomUser.objects.unfiltered().get(id=user_id)
            else:
                # 区域管理员只能删除同区域的用户
                user_to_delete = CustomUser.objects.get(id=user_id, region=current_user.region)

            # 不能删除自己
            if user_to_delete.id == current_user.id:
                return Response({
                    'code': 400,
                    'msg': '不能删除自己',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查是否有关联数据需要处理
            if user_to_delete.role == CustomUser.Role.MEMBER_ASSISTANT:
                # 如果删除的是助理，需要处理其负责的会员
                managed_members = CustomUser.objects.filter(managed_by=user_to_delete)
                if managed_members.exists():
                    # 将这些会员的助理关联设为空
                    managed_members.update(managed_by=None)

            # 软删除：设置为非活跃状态
            user_to_delete.is_active = False
            user_to_delete.save()

            return Response({
                'code': 200,
                'msg': '用户删除成功',
                'data': None
            })

        except CustomUser.DoesNotExist:
            return Response({
                'code': 404,
                'msg': '用户不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'删除用户失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PermissionListAPIView(APIView):
    """权限列表API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取权限列表"""
        current_user = request.user

        # 只有管理员可以查看权限列表
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以查看权限列表',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 直接查询权限表
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id, name, code, description, category
                    FROM core_permission
                    ORDER BY category, name
                """)
                permissions = []
                for row in cursor.fetchall():
                    permissions.append({
                        'id': row[0],
                        'name': row[1],
                        'code': row[2],
                        'description': row[3],
                        'category': row[4]
                    })

            # 按分类分组
            categories = {}
            for perm in permissions:
                category = perm['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append(perm)

            return Response({
                'code': 200,
                'msg': '权限列表获取成功',
                'data': {
                    'permissions': permissions,
                    'categories': categories
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取权限列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserRefreshTokenAPIView(APIView):
    """用户刷新令牌查看API"""
    permission_classes = [IsAuthenticated]

    def get(self, request, user_id):
        """获取用户的刷新令牌"""
        current_user = request.user

        # 权限检查：只有管理员可以查看刷新令牌
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以查看刷新令牌',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 查找目标用户
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                user = CustomUser.objects.unfiltered().get(id=user_id)
            else:
                user = CustomUser.objects.get(id=user_id, region=current_user.region)

            return Response({
                'code': 200,
                'msg': '获取刷新令牌成功',
                'data': {
                    'username': user.username,
                    'refresh_token': user.refresh_token or '暂无刷新令牌'
                }
            })

        except CustomUser.DoesNotExist:
            return Response({
                'code': 404,
                'msg': '用户不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取刷新令牌失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MemberListAPIView(APIView):
    """获取会员列表API（用于助理选择负责的会员）"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取会员列表"""
        current_user = request.user

        # 权限检查：只有管理员可以查看会员列表
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以查看会员列表',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 根据当前用户角色决定查询范围
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                # 超级管理员可以看到所有区域的会员
                members = CustomUser.objects.unfiltered().filter(
                    role=CustomUser.Role.MEMBER,
                    is_active=True
                )
            else:
                # 区域管理员只能看到同区域的会员
                members = CustomUser.objects.filter(
                    role=CustomUser.Role.MEMBER,
                    region=current_user.region,
                    is_active=True
                )

            # 构建响应数据
            member_list = []
            for member in members:
                member_data = {
                    'id': member.id,
                    'username': member.username,
                    'phone': member.phone,
                    'regionName': member.region.name if member.region else '全局管理',
                    'managed_by': {
                        'id': member.managed_by.id,
                        'username': member.managed_by.username
                    } if member.managed_by else None
                }
                member_list.append(member_data)

            return Response({
                'code': 200,
                'msg': '会员列表获取成功',
                'data': member_list
            })

        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取会员列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
